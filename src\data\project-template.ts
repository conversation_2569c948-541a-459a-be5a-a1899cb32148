import { type Project } from './projects'

// Template for adding new projects
// Copy this template and modify it for your new project

export const newProjectTemplate: Project = {
  id: "project-name", // Will be auto-generated from title if using createNewProject
  title: "Your Project Title",
  description: "A comprehensive description of your project. Explain what it does, what problems it solves, and what makes it special. Include key features and technologies used.",
  images: [
    "/images/projects/your-project/screenshot1.jpg",
    "/images/projects/your-project/screenshot2.jpg",
    "/images/projects/your-project/screenshot3.jpg",
    // Add more images as needed
  ],
  tags: [
    "React", 
    "TypeScript", 
    "Tailwind CSS",
    // Add your technologies here
  ],
  category: "Web Application", // or "Mobile Application"
  github: "https://github.com/yourusername/your-project", // Optional
  liveLink: "https://your-project.vercel.app", // Optional
  featured: false, // Set to true for featured projects
  url: "your-project.vercel.app", // For web projects only (shows in browser frame)
  status: "completed", // "completed", "in-progress", or "planned"
  startDate: "2024-01", // Optional
  endDate: "2024-03" // Optional
}

// Example: Web Application Project
export const webAppExample: Project = {
  id: "task-management-app",
  title: "Task Management App",
  description: "A modern task management application built with React and TypeScript. Features include drag-and-drop task organization, real-time collaboration, deadline tracking, and team management. Designed with a clean, intuitive interface for maximum productivity.",
  images: [
    "/images/projects/task-app/dashboard.jpg",
    "/images/projects/task-app/kanban-board.jpg",
    "/images/projects/task-app/team-view.jpg",
  ],
  tags: ["React", "TypeScript", "Tailwind CSS", "Framer Motion", "React DnD", "Socket.io"],
  category: "Web Application",
  github: "https://github.com/0phl/task-management-app",
  liveLink: "https://task-app.ronandelacruz.me",
  featured: true,
  url: "task-app.ronandelacruz.me",
  status: "completed",
  startDate: "2024-02",
  endDate: "2024-04"
}

// Example: Mobile Application Project
export const mobileAppExample: Project = {
  id: "fitness-tracker-mobile",
  title: "Fitness Tracker Mobile App",
  description: "A comprehensive fitness tracking mobile application built with React Native. Track workouts, monitor progress, set goals, and connect with friends. Features offline support, health kit integration, and personalized workout recommendations.",
  images: [
    "/images/projects/fitness-app/home-screen.jpg",
    "/images/projects/fitness-app/workout-tracking.jpg",
    "/images/projects/fitness-app/progress-charts.jpg",
  ],
  tags: ["React Native", "TypeScript", "Expo", "Firebase", "Redux Toolkit", "React Query"],
  category: "Mobile Application",
  github: "https://github.com/0phl/fitness-tracker-mobile",
  liveLink: "https://play.google.com/store/apps/details?id=com.ronan.fitness",
  featured: true,
  status: "in-progress",
  startDate: "2024-03"
  // No URL needed for mobile apps
}

// Quick add function - use this to easily add projects
export const quickAddProject = (
  title: string,
  description: string,
  tags: string[],
  category: "Web Application" | "Mobile Application",
  options: {
    github?: string
    liveLink?: string
    featured?: boolean
    url?: string
    images?: string[]
    status?: "completed" | "in-progress" | "planned"
  } = {}
): Project => {
  return {
    id: title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
    title,
    description,
    images: options.images || ["/placeholder.svg?height=400&width=600"],
    tags,
    category,
    github: options.github,
    liveLink: options.liveLink,
    featured: options.featured || false,
    url: options.url,
    status: options.status || "completed"
  }
}

// Usage example:
// const myProject = quickAddProject(
//   "My Awesome Project",
//   "This is an amazing project that does cool things.",
//   ["React", "TypeScript", "Tailwind"],
//   "Web Application",
//   {
//     github: "https://github.com/0phl/my-project",
//     liveLink: "https://my-project.vercel.app",
//     featured: true,
//     url: "my-project.vercel.app"
//   }
// )

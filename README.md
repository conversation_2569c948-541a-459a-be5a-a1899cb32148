
# Portfolio Website Tech Stack

## Core Technologies

- **React**: Frontend library for building the user interface
- **TypeScript**: Type-safe JavaScript for better development experience
- **Vite**: Fast build tool and development server

## Styling & UI

- **Tailwind CSS**: Utility-first CSS framework for styling
- **CSS Animations**: Custom animations for enhanced user experience
- **Devicon**: Icon library for technology logos

## Components & Features

- **Lucide React**: Lightweight icon library
- **Custom Hooks**: Including useTypewriter for text animation effects

## Getting Started

1. Run `npm install`
2. Run `npm run dev`

## Project Structure

- `/src/components`: UI components (HeroSection, SkillsSection, etc.)
- `/src/hooks`: Custom React hooks
- `/src/lib`: Utility functions
- `/public`: Static assets

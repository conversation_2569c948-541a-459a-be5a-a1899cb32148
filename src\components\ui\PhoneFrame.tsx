import type React from "react"

interface PhoneFrameProps {
  children: React.ReactNode
  className?: string
  model?: "iphone" | "android"
  showStatusBar?: boolean
  time?: string
  isGridPreview?: boolean
  isModal?: boolean
  color?: "purple" | "silver" | "black" | "gold"
}

const PhoneFrame: React.FC<PhoneFrameProps> = ({
  children,
  className = "",
  model = "iphone",
  showStatusBar = true,
  time = "9:41",
  isGridPreview = false,
  isModal = false,
  color = "purple"
}) => {
  // Use smaller sizing for grid preview, medium for modal, and full for regular view
  const isSmall = isGridPreview
  const isMedium = isModal && !isGridPreview

  // iPhone 14 Pro dimensions and styling
  const frameHeight = isSmall ? "h-[217px]" : isMedium ? "h-[434px]" : "h-[868px]"
  const frameWidth = isSmall ? "w-[107px]" : isMedium ? "w-[214px]" : "w-[428px]"
  const screenHeight = isSmall ? "h-[207.5px]" : isMedium ? "h-[415px]" : "h-[830px]"
  const screenWidth = isSmall ? "w-[97.5px]" : isMedium ? "w-[195px]" : "w-[390px]"
  const framePadding = isSmall ? "p-[4.75px]" : isMedium ? "p-[9.5px]" : "p-[19px]"
  const frameRadius = isSmall ? "rounded-[17px]" : isMedium ? "rounded-[34px]" : "rounded-[68px]"
  const screenRadius = isSmall ? "rounded-[12.25px]" : isMedium ? "rounded-[24.5px]" : "rounded-[49px]"

  // Color variants based on the SCSS design
  const colorVariants = {
    purple: {
      frame: "#342C3F",
      frameDark: "#2a2235",
      frameLight: "#8a7d96",
      buttons: "#2a2235"
    },
    silver: {
      frame: "#e2e3e4",
      frameDark: "#c8c9ca",
      frameLight: "#ffffff",
      buttons: "#c8c9ca"
    },
    black: {
      frame: "#76726F",
      frameDark: "#5e5a57",
      frameLight: "#c4c0bd",
      buttons: "#5e5a57"
    },
    gold: {
      frame: "#F6EEDB",
      frameDark: "#d1c4a3",
      frameLight: "#ffffff",
      buttons: "#d1c4a3"
    }
  }

  const currentColor = colorVariants[color]

  // Dynamic Island dimensions
  const dynamicIslandWidth = isSmall ? "w-[30px]" : isMedium ? "w-[60px]" : "w-[120px]"
  const dynamicIslandHeight = isSmall ? "h-[8.75px]" : isMedium ? "h-[17.5px]" : "h-[35px]"
  const dynamicIslandTop = isSmall ? "top-[7.25px]" : isMedium ? "top-[14.5px]" : "top-[29px]"
  const dynamicIslandRadius = isSmall ? "rounded-[5px]" : isMedium ? "rounded-[10px]" : "rounded-[20px]"

  // Camera and sensor dimensions
  const cameraWidth = isSmall ? "w-[18.5px]" : isMedium ? "w-[37px]" : "w-[74px]"
  const cameraHeight = isSmall ? "h-[8.25px]" : isMedium ? "h-[16.5px]" : "h-[33px]"
  const cameraRadius = isSmall ? "rounded-[4.25px]" : isMedium ? "rounded-[8.5px]" : "rounded-[17px]"
  const cameraTop = isSmall ? "top-[7.5px]" : isMedium ? "top-[15px]" : "top-[30px]"

  const sensorSize = isSmall ? "w-[2.25px] h-[2.25px]" : isMedium ? "w-[4.5px] h-[4.5px]" : "w-[9px] h-[9px]"
  const sensorTop = isSmall ? "top-[10.5px]" : isMedium ? "top-[21px]" : "top-[42px]"
  const sensorRight = isSmall ? "right-[9px]" : isMedium ? "right-[18px]" : "right-[36px]"

  // Button dimensions
  const buttonWidth = isSmall ? "w-[0.75px]" : isMedium ? "w-[1.5px]" : "w-[3px]"
  const muteButtonHeight = isSmall ? "h-[8px]" : isMedium ? "h-[16px]" : "h-[32px]"
  const volumeButtonHeight = isSmall ? "h-[15.5px]" : isMedium ? "h-[31px]" : "h-[62px]"
  const powerButtonHeight = isSmall ? "h-[25px]" : isMedium ? "h-[50px]" : "h-[100px]"

  const muteButtonTop = isSmall ? "top-[28.75px]" : isMedium ? "top-[57.5px]" : "top-[115px]"
  const volumeUpTop = isSmall ? "top-[43.75px]" : isMedium ? "top-[87.5px]" : "top-[175px]"
  const volumeDownTop = isSmall ? "top-[78.75px]" : isMedium ? "top-[157.5px]" : "top-[315px]"
  const powerButtonTop = isSmall ? "top-[50px]" : isMedium ? "top-[100px]" : "top-[200px]"

  // Status bar and content adjustments
  const statusBarPadding = isSmall ? "px-2 pt-3 pb-0.5" : isMedium ? "px-4 pt-6 pb-1" : "px-6 pt-8 pb-2"
  const statusBarText = isSmall ? "text-[8px]" : isMedium ? "text-xs" : "text-sm"
  const statusBarIcon = isSmall ? "w-2 h-2" : isMedium ? "w-3 h-3" : "w-4 h-4"
  const batteryIcon = isSmall ? "w-3 h-1.5" : isMedium ? "w-5 h-2.5" : "w-6 h-3"
  const contentPadding = showStatusBar ? (isSmall ? "pt-8" : isMedium ? "pt-14" : "pt-20") : isSmall ? "pt-4" : isMedium ? "pt-6" : "pt-8"
  const homeIndicator = isSmall ? "w-16 h-0.5" : isMedium ? "w-24 h-1" : "w-32 h-1.5"

  return (
    <div className={`relative mx-auto ${className} ${isModal ? 'min-h-[300px] sm:min-h-[400px]' : ''}`}>
      {/* iPhone 14 Pro Frame */}
      <div
        className={`relative ${frameHeight} ${frameWidth} mx-auto`}
        style={{
          background: '#010101',
          border: `1px solid ${currentColor.frameDark}`,
          borderRadius: isSmall ? '17px' : isMedium ? '34px' : '68px',
          boxShadow: `inset 0 0 ${isSmall ? '1px' : isMedium ? '2px' : '4px'} ${isSmall ? '0.5px' : isMedium ? '1px' : '2px'} ${currentColor.frameLight}, inset 0 0 0 ${isSmall ? '1.5px' : isMedium ? '3px' : '6px'} ${currentColor.frame}`,
          padding: isSmall ? '4.75px' : isMedium ? '9.5px' : '19px'
        }}
      >
        {/* Screen */}
        <div
          className={`${screenHeight} ${screenWidth} relative overflow-hidden`}
          style={{
            borderRadius: isSmall ? '12.25px' : isMedium ? '24.5px' : '49px',
            background: '#000'
          }}
        >
          {/* Dynamic Island */}
          {model === "iphone" && (
            <div
              className={`absolute ${dynamicIslandTop} left-1/2 transform -translate-x-1/2 ${dynamicIslandWidth} ${dynamicIslandHeight} z-30`}
              style={{
                background: '#010101',
                borderRadius: isSmall ? '5px' : isMedium ? '10px' : '20px'
              }}
            />
          )}

          {/* Camera and Sensors */}
          {model === "iphone" && (
            <>
              {/* Camera area */}
              <div
                className={`absolute ${cameraTop} left-1/2 transform -translate-x-1/2 ${cameraWidth} ${cameraHeight} z-30`}
                style={{
                  background: '#010101',
                  borderRadius: isSmall ? '4.25px' : isMedium ? '8.5px' : '17px'
                }}
              />
              {/* Front camera sensor */}
              <div
                className={`absolute ${sensorTop} ${sensorSize} z-30`}
                style={{
                  background: 'radial-gradient(farthest-corner at 20% 20%, #6074BF 0, transparent 40%), radial-gradient(farthest-corner at 80% 80%, #513785 0, #24555E 20%, transparent 50%)',
                  borderRadius: '50%',
                  right: isSmall ? '9px' : isMedium ? '18px' : '36px',
                  marginLeft: isSmall ? '9px' : isMedium ? '18px' : '36px'
                }}
              />
            </>
          )}

          {/* Status Bar */}
          {showStatusBar && (
            <div
              className={`absolute top-0 left-0 right-0 z-20 bg-white ${statusBarPadding} flex justify-between items-center text-black ${statusBarText} font-semibold`}
            >
              <span>{time}</span>
              <div className="flex items-center gap-1">
                <svg className={`${statusBarIcon} ml-1`} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" />
                </svg>
                <div className={`${batteryIcon} border border-black rounded-sm ml-1 relative p-px`}>
                  <div className="w-full h-full bg-green-500 rounded-sm"></div>
                  <div className="absolute -right-px top-1/2 -translate-y-1/2 w-px h-1/2 bg-black rounded-r-sm"></div>
                </div>
              </div>
            </div>
          )}

          {/* App Content */}
          <div className={`${contentPadding} h-full bg-white overflow-hidden`}>
            {children}
          </div>

          {/* Home Indicator */}
          {model === "iphone" && (
            <div
              className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 ${homeIndicator} bg-white/40 rounded-full`}
            />
          )}
        </div>

        {/* Side Buttons */}
        {model === "iphone" && (
          <>
            {/* Mute Button */}
            <div
              className={`absolute ${muteButtonTop} -left-0.5 ${buttonWidth} ${muteButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Volume Up Button */}
            <div
              className={`absolute ${volumeUpTop} -left-0.5 ${buttonWidth} ${volumeButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Volume Down Button */}
            <div
              className={`absolute ${volumeDownTop} -left-0.5 ${buttonWidth} ${volumeButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Power Button */}
            <div
              className={`absolute ${powerButtonTop} -right-0.5 ${buttonWidth} ${powerButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />
          </>
        )}
      </div>
    </div>
  )
}

export default PhoneFrame

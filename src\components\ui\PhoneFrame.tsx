import type React from "react"

interface PhoneFrameProps {
  children: React.ReactNode
  className?: string
  model?: "iphone" | "android"
  showStatusBar?: boolean
  time?: string
  isGridPreview?: boolean
  isModal?: boolean
}

const PhoneFrame: React.FC<PhoneFrameProps> = ({
  children,
  className = "",
  model = "iphone",
  showStatusBar = true,
  time = "9:41",
  isGridPreview = false,
  isModal = false
}) => {
  // Use smaller sizing for grid preview, medium for modal, and full for regular view
  const isSmall = isGridPreview
  const isMedium = isModal && !isGridPreview

  // Balanced radii for realistic phone shape
  const outerRadius = isSmall
    ? "rounded-t-[2.8rem] rounded-b-[2rem]"
    : isMedium
    ? "rounded-t-[3rem] rounded-b-[2.5rem]"
    : "rounded-t-[3.5rem] rounded-b-[2.8rem]"
  const innerRadius = isSmall
    ? "rounded-t-[2.5rem] rounded-b-[1.8rem]"
    : isMedium
    ? "rounded-t-[2.7rem] rounded-b-[2.2rem]"
    : "rounded-t-[3rem] rounded-b-[2.4rem]"
  const notchContainer = isSmall ? "w-20 h-4" : isMedium ? "w-28 h-5" : "w-32 h-6"
  const cameraDot = isSmall ? "w-1.5 h-1.5" : "w-2.5 h-2.5"
  const speaker = isSmall ? "w-5 h-0.5" : isMedium ? "w-7 h-1" : "w-8 h-1"
  const statusBarPadding = isSmall ? "px-3 pt-6 pb-1" : isMedium ? "px-5 pt-7 pb-1.5" : "px-6 pt-8 pb-2"
  const statusBarText = isSmall ? "text-[10px]" : isMedium ? "text-xs" : "text-sm"
  const statusBarIcon = isSmall ? "w-3 h-3" : "w-4 h-4"
  const batteryIcon = isSmall ? "w-4 h-2" : isMedium ? "w-5 h-2.5" : "w-6 h-3"
  const contentPadding = showStatusBar ? (isSmall ? "pt-10" : isMedium ? "pt-12" : "pt-16") : isSmall ? "pt-5" : isMedium ? "pt-6" : "pt-8"
  const homeIndicator = isSmall ? "w-24 h-0.5" : isMedium ? "w-32 h-1" : "w-36 h-1.5"
  const framePadding = isSmall ? "p-0.5" : isMedium ? "p-1" : "p-1.5"
  const frameBorder = isSmall ? "border" : "border-2"
  const shadow = isSmall
    ? "blur-xl transform translate-y-2"
    : "blur-2xl transform translate-y-4 scale-105"
  const chinRadius = isSmall
    ? "rounded-b-[2rem]"
    : isMedium
    ? "rounded-b-[2.5rem]"
    : "rounded-b-[2.8rem]"
  const chinHeight = isSmall ? "h-4" : "h-6"

  return (
    <div className={`relative mx-auto ${className} ${isModal ? 'min-h-[300px] sm:min-h-[400px]' : ''}`}>
      {/* Phone Shadow */}
      <div
        className={`absolute inset-0 bg-black/30 ${outerRadius} ${shadow}`}
      ></div>
      {/* Subtle bottom shadow for chin */}
      <div className={`absolute left-0 right-0 bottom-0 ${chinHeight} bg-gradient-to-t from-black/40 to-transparent ${chinRadius} z-10 pointer-events-none`}></div>
      {/* Phone Outer Frame */}
      <div
        className={`relative bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 ${outerRadius} ${framePadding} h-full shadow-2xl ${frameBorder} border-gray-700`}
      >
        {/* Phone Inner Frame */}
        <div className={`bg-black ${innerRadius} h-full relative overflow-hidden`}>
          {/* Dynamic Island / Notch */}
          {model === "iphone" && (
            <div
              className={`absolute top-1 left-1/2 transform -translate-x-1/2 ${notchContainer} bg-black rounded-full z-30 border border-gray-800 shadow-inner`}
            >
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center gap-3">
                <div className={`${cameraDot} bg-gray-900 rounded-full border border-gray-700`}></div>
                <div className={`${speaker} bg-gray-900 rounded-full`}></div>
              </div>
            </div>
          )}

          {/* Status Bar */}
          {showStatusBar && (
            <div
              className={`absolute top-0 left-0 right-0 z-20 bg-white ${statusBarPadding} flex justify-between items-center text-black ${statusBarText} font-semibold`}
            >
              <span>{time}</span>
              <div className="flex items-center gap-1">
                <svg className={`${statusBarIcon} ml-1`} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" />
                </svg>
                <div className={`${batteryIcon} border border-black rounded-sm ml-1 relative p-px`}>
                  <div className="w-full h-full bg-green-500 rounded-sm"></div>
                  <div className="absolute -right-px top-1/2 -translate-y-1/2 w-px h-1/2 bg-black rounded-r-sm"></div>
                </div>
              </div>
            </div>
          )}

          {/* App Content */}
          <div className={`${contentPadding} h-full bg-white overflow-hidden`}>
            {children}
          </div>

          {/* Home Indicator */}
          {model === "iphone" && (
            <div
              className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 ${homeIndicator} bg-white/40 rounded-full`}
            ></div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PhoneFrame

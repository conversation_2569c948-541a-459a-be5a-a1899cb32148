import type React from "react"

interface PhoneFrameProps {
  children: React.ReactNode
  className?: string
  model?: "iphone" | "android"
  showStatusBar?: boolean
  time?: string
  isGridPreview?: boolean
  isModal?: boolean
  color?: "purple" | "silver" | "black" | "gold"
}

const PhoneFrame: React.FC<PhoneFrameProps> = ({
  children,
  className = "",
  model = "iphone",
  showStatusBar = true,
  time = "9:41",
  isGridPreview = false,
  isModal = false,
  color = "gold"
}) => {
  // Use smaller sizing for grid preview, medium for modal, and full for regular view
  const isSmall = isGridPreview
  const isMedium = isModal && !isGridPreview

  // iPhone 14 Pro dimensions and styling - responsive sizing for mobile
  const frameHeight = isSmall ? "h-[200px] sm:h-[300px]" : isMedium ? "h-[400px] sm:h-[500px]" : "h-[868px]"
  const frameWidth = isSmall ? "w-[98px] sm:w-[148px]" : isMedium ? "w-[196px] sm:w-[246px]" : "w-[428px]"
  const screenHeight = isSmall ? "h-[191px] sm:h-[287px]" : isMedium ? "h-[382px] sm:h-[478px]" : "h-[830px]"
  const screenWidth = isSmall ? "w-[90px] sm:w-[135px]" : isMedium ? "w-[180px] sm:w-[225px]" : "w-[390px]"
  const framePadding = isSmall ? "p-[6.5px]" : isMedium ? "p-[11px]" : "p-[19px]"
  const frameRadius = isSmall ? "rounded-[23px]" : isMedium ? "rounded-[39px]" : "rounded-[68px]"
  const screenRadius = isSmall ? "rounded-[17px]" : isMedium ? "rounded-[28px]" : "rounded-[49px]"

  // Color variants based on the SCSS design
  const colorVariants = {
    purple: {
      frame: "#342C3F",
      frameDark: "#2a2235",
      frameLight: "#8a7d96",
      buttons: "#2a2235"
    },
    silver: {
      frame: "#e2e3e4",
      frameDark: "#c8c9ca",
      frameLight: "#ffffff",
      buttons: "#c8c9ca"
    },
    black: {
      frame: "#76726F",
      frameDark: "#5e5a57",
      frameLight: "#c4c0bd",
      buttons: "#5e5a57"
    },
    gold: {
      frame: "#F6EEDB",
      frameDark: "#d1c4a3",
      frameLight: "#ffffff",
      buttons: "#d1c4a3"
    }
  }

  const currentColor = colorVariants[color]

  // Dynamic Island dimensions - responsive for mobile
  const dynamicIslandWidth = isSmall ? "w-[28px] sm:w-[42px]" : isMedium ? "w-[55px] sm:w-[69px]" : "w-[120px]"
  const dynamicIslandHeight = isSmall ? "h-[8px] sm:h-[12px]" : isMedium ? "h-[16px] sm:h-[20px]" : "h-[35px]"
  const dynamicIslandTop = isSmall ? "top-[6.5px] sm:top-[10px]" : isMedium ? "top-[13.5px] sm:top-[17px]" : "top-[29px]"
  const dynamicIslandRadius = isSmall ? "rounded-[4.5px] sm:rounded-[7px]" : isMedium ? "rounded-[9.5px] sm:rounded-[12px]" : "rounded-[20px]"

  // Camera and sensor dimensions - responsive for mobile
  const cameraWidth = isSmall ? "w-[17px] sm:w-[26px]" : isMedium ? "w-[34px] sm:w-[43px]" : "w-[74px]"
  const cameraHeight = isSmall ? "h-[7.5px] sm:h-[11.5px]" : isMedium ? "h-[15px] sm:h-[19px]" : "h-[33px]"
  const cameraRadius = isSmall ? "rounded-[4px] sm:rounded-[6px]" : isMedium ? "rounded-[8px] sm:rounded-[10px]" : "rounded-[17px]"
  const cameraTop = isSmall ? "top-[7px] sm:top-[10.5px]" : isMedium ? "top-[14px] sm:top-[17.5px]" : "top-[30px]"

  const sensorSize = isSmall ? "w-[2px] h-[2px] sm:w-[3px] sm:h-[3px]" : isMedium ? "w-[4px] h-[4px] sm:w-[5px] sm:h-[5px]" : "w-[9px] h-[9px]"
  const sensorTop = isSmall ? "top-[9.5px] sm:top-[14.5px]" : isMedium ? "top-[19px] sm:top-[24px]" : "top-[42px]"
  const sensorRight = isSmall ? "right-[8px] sm:right-[12.5px]" : isMedium ? "right-[17px] sm:right-[21px]" : "right-[36px]"

  // Button dimensions - responsive for mobile
  const buttonWidth = isSmall ? "w-[0.7px] sm:w-[1px]" : isMedium ? "w-[1.3px] sm:w-[1.7px]" : "w-[3px]"
  const muteButtonHeight = isSmall ? "h-[7px] sm:h-[11px]" : isMedium ? "h-[14px] sm:h-[18px]" : "h-[32px]"
  const volumeButtonHeight = isSmall ? "h-[14px] sm:h-[21px]" : isMedium ? "h-[28px] sm:h-[36px]" : "h-[62px]"
  const powerButtonHeight = isSmall ? "h-[23px] sm:h-[35px]" : isMedium ? "h-[46px] sm:h-[58px]" : "h-[100px]"

  const muteButtonTop = isSmall ? "top-[26px] sm:top-[40px]" : isMedium ? "top-[52px] sm:top-[66px]" : "top-[115px]"
  const volumeUpTop = isSmall ? "top-[40px] sm:top-[60px]" : isMedium ? "top-[80px] sm:top-[100px]" : "top-[175px]"
  const volumeDownTop = isSmall ? "top-[72px] sm:top-[109px]" : isMedium ? "top-[144px] sm:top-[181px]" : "top-[315px]"
  const powerButtonTop = isSmall ? "top-[46px] sm:top-[69px]" : isMedium ? "top-[92px] sm:top-[115px]" : "top-[200px]"

  // Status bar and content adjustments - responsive for mobile
  const statusBarPadding = isSmall ? "px-2 pt-2.5 pb-0.5 sm:px-3 sm:pt-4 sm:pb-1" : isMedium ? "px-4 pt-5.5 pb-1 sm:px-5 sm:pt-7 sm:pb-1.5" : "px-6 pt-8 pb-2"
  const statusBarText = isSmall ? "text-[7px] sm:text-[10px]" : isMedium ? "text-[10px] sm:text-xs" : "text-sm"
  const statusBarIcon = isSmall ? "w-2 h-2 sm:w-3 sm:h-3" : isMedium ? "w-3 h-3 sm:w-4 sm:h-4" : "w-4 h-4"
  const batteryIcon = isSmall ? "w-2.5 h-1.5 sm:w-4 sm:h-2" : isMedium ? "w-4.5 h-2.5 sm:w-6 sm:h-3" : "w-6 h-3"
  const contentPadding = showStatusBar ? (isSmall ? "pt-8 sm:pt-12" : isMedium ? "pt-14 sm:pt-18" : "pt-20") : isSmall ? "pt-4 sm:pt-6" : isMedium ? "pt-6 sm:pt-8" : "pt-8"
  const homeIndicator = isSmall ? "w-13 h-0.5 sm:w-20 sm:h-1" : isMedium ? "w-22 h-1 sm:w-28 sm:h-1.5" : "w-32 h-1.5"

  return (
    <div className={`relative mx-auto ${className} ${isModal ? 'min-h-[300px] sm:min-h-[400px]' : ''}`}>
      {/* iPhone 14 Pro Frame */}
      <div
        className={`relative ${frameHeight} ${frameWidth} mx-auto`}
        style={{
          background: '#010101',
          border: `1px solid ${currentColor.frameDark}`,
          borderRadius: isSmall ? '15px' : isMedium ? '31px' : '68px',
          boxShadow: `inset 0 0 ${isSmall ? '1px' : isMedium ? '2px' : '4px'} ${isSmall ? '0.5px' : isMedium ? '1px' : '2px'} ${currentColor.frameLight}, inset 0 0 0 ${isSmall ? '1.5px' : isMedium ? '3px' : '6px'} ${currentColor.frame}`,
          padding: isSmall ? '4.5px' : isMedium ? '8.5px' : '19px'
        }}
      >
        {/* Screen */}
        <div
          className={`${screenHeight} ${screenWidth} relative overflow-hidden`}
          style={{
            borderRadius: isSmall ? '11px' : isMedium ? '23px' : '49px',
            background: '#000'
          }}
        >
          {/* Dynamic Island */}
          {model === "iphone" && (
            <div
              className={`absolute ${dynamicIslandTop} left-1/2 transform -translate-x-1/2 ${dynamicIslandWidth} ${dynamicIslandHeight} z-30`}
              style={{
                background: '#010101',
                borderRadius: isSmall ? '4.5px' : isMedium ? '9.5px' : '20px'
              }}
            />
          )}

          {/* Camera and Sensors */}
          {model === "iphone" && (
            <>
              {/* Camera area */}
              <div
                className={`absolute ${cameraTop} left-1/2 transform -translate-x-1/2 ${cameraWidth} ${cameraHeight} z-30`}
                style={{
                  background: '#010101',
                  borderRadius: isSmall ? '4px' : isMedium ? '8px' : '17px'
                }}
              />
              {/* Front camera sensor */}
              <div
                className={`absolute ${sensorTop} ${sensorSize} z-30`}
                style={{
                  background: 'radial-gradient(farthest-corner at 20% 20%, #6074BF 0, transparent 40%), radial-gradient(farthest-corner at 80% 80%, #513785 0, #24555E 20%, transparent 50%)',
                  borderRadius: '50%',
                  right: isSmall ? '8px' : isMedium ? '17px' : '36px',
                  marginLeft: isSmall ? '8px' : isMedium ? '17px' : '36px'
                }}
              />
            </>
          )}

          {/* Status Bar */}
          {showStatusBar && (
            <div
              className={`absolute top-0 left-0 right-0 z-20 bg-white ${statusBarPadding} flex justify-between items-center text-black ${statusBarText} font-semibold`}
            >
              <span>{time}</span>
              <div className="flex items-center gap-1">
                <svg className={`${statusBarIcon} ml-1`} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" />
                </svg>
                <div className={`${batteryIcon} border border-black rounded-sm ml-1 relative p-px`}>
                  <div className="w-full h-full bg-green-500 rounded-sm"></div>
                  <div className="absolute -right-px top-1/2 -translate-y-1/2 w-px h-1/2 bg-black rounded-r-sm"></div>
                </div>
              </div>
            </div>
          )}

          {/* App Content */}
          <div className={`${contentPadding} h-full bg-white overflow-hidden`}>
            {children}
          </div>

          {/* Home Indicator */}
          {model === "iphone" && (
            <div
              className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 ${homeIndicator} bg-white/40 rounded-full`}
            />
          )}
        </div>

        {/* Side Buttons */}
        {model === "iphone" && (
          <>
            {/* Mute Button */}
            <div
              className={`absolute ${muteButtonTop} -left-0.5 ${buttonWidth} ${muteButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Volume Up Button */}
            <div
              className={`absolute ${volumeUpTop} -left-0.5 ${buttonWidth} ${volumeButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Volume Down Button */}
            <div
              className={`absolute ${volumeDownTop} -left-0.5 ${buttonWidth} ${volumeButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />

            {/* Power Button */}
            <div
              className={`absolute ${powerButtonTop} -right-0.5 ${buttonWidth} ${powerButtonHeight} z-10`}
              style={{
                background: currentColor.buttons,
                borderRadius: '2px'
              }}
            />
          </>
        )}
      </div>
    </div>
  )
}

export default PhoneFrame

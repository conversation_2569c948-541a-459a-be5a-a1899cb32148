{"name": "ronan-portfolio", "version": "0.0.1", "private": true, "license": "MIT", "type": "module", "homepage": "https://ronandelacruz.me", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"clsx": "latest", "framer-motion": "^12.10.4", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "latest"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "gh-pages": "^6.3.0", "postcss": "latest", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "3.4.17", "typescript": "^5.5.4", "vite": "^5.2.0"}}
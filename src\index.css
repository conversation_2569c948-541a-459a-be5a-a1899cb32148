@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  :root[class~="dark"] {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem; /* Adjust based on your header height */
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Animation utilities */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1 hover:shadow-md;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes caretBlink {
    from, to { opacity: 1; }
    50% { opacity: 0; }
  }
  
  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    25% {
      transform: translateY(-6px);
    }
    50% {
      transform: translateY(-2px);
    }
    75% {
      transform: translateY(-4px);
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3);
    }
    
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 6px rgba(255, 255, 255, 0);
    }
    
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
  }
  
  @keyframes ripple {
    to {
      transform: scale(2);
      opacity: 0;
    }
  }

  @keyframes progressAnimation {
    from { width: 0%; }
    to { width: attr(data-width); }
  }

  /* Apply animations with adjusted timing */
  .animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-in-out forwards;
    animation-fill-mode: both;
  }

  .animate-slide-up {
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
    animation-fill-mode: both;
  }

  .animate-caret-blink {
    animation: caretBlink 0.8s ease-out infinite;
  }
  
  .animate-bounce {
    animation: bounce 1s cubic-bezier(.36,.07,.19,.97) infinite;
  }
  
  .animate-pulse {
    animation: pulse 2s infinite;
  }
  
  .animate-ripple {
    animation: ripple 0.7s linear;
  }

  /* Preloaded placeholders */
  .image-placeholder {
    background-color: hsl(var(--muted));
    position: relative;
    overflow: hidden;
  }
  
  .image-placeholder::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    100% {
      left: 100%;
    }
  }

  /* Fix for progress bars animation */
  [data-width] {
    animation: none !important;
    width: var(--width) !important;
  }

  [data-width="0%"] { --width: 0%; }
  [data-width="10%"] { --width: 10%; }
  [data-width="20%"] { --width: 20%; }
  [data-width="30%"] { --width: 30%; }
  [data-width="40%"] { --width: 40%; }
  [data-width="50%"] { --width: 50%; }
  [data-width="60%"] { --width: 60%; }
  [data-width="70%"] { --width: 70%; }
  [data-width="80%"] { --width: 80%; }
  [data-width="90%"] { --width: 90%; }
  [data-width="100%"] { --width: 100%; }
}

/* Add to root styles */
img, video {
  display: block;
  max-width: 100%;
}

img.lazy-load {
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

img.lazy-loaded {
  opacity: 1;
}

/* Add shadow glow effect for timeline dots */
.shadow-glow {
  box-shadow: 0 0 8px 2px hsl(var(--primary) / 0.3);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}